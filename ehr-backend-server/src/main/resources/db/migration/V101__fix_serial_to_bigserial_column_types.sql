-- Migration to fix SERIAL to BIGSERIAL column type mismatches
-- This migration addresses Hibernate schema validation errors where entities expect BIGINT but database has INTEGER

-- ===================================================================
-- Step 1: Store view definitions for recreation (if any depend on these tables)
-- ===================================================================

DO $$
DECLARE
    patient_summary_definition TEXT;
    recent_registrations_definition TEXT;
BEGIN
    -- Get the view definitions if they exist
    BEGIN
        SELECT pg_get_viewdef('patient_summary', true) INTO patient_summary_definition;
    EXCEPTION WHEN undefined_table THEN
        patient_summary_definition := NULL;
    END;
    
    BEGIN
        SELECT pg_get_viewdef('recent_patient_registrations', true) INTO recent_registrations_definition;
    EXCEPTION WHEN undefined_table THEN
        recent_registrations_definition := NULL;
    END;
    
    -- Create a temporary table to store view definitions
    CREATE TEMP TABLE IF NOT EXISTS temp_view_definitions_v101 (
        view_name VARCHAR(100),
        view_definition TEXT
    );
    
    -- Store the definitions if they exist
    IF patient_summary_definition IS NOT NULL THEN
        INSERT INTO temp_view_definitions_v101 VALUES ('patient_summary', patient_summary_definition);
    END IF;
    
    IF recent_registrations_definition IS NOT NULL THEN
        INSERT INTO temp_view_definitions_v101 VALUES ('recent_patient_registrations', recent_registrations_definition);
    END IF;
END $$;

-- ===================================================================
-- Step 2: Drop dependent views temporarily
-- ===================================================================

DROP VIEW IF EXISTS recent_patient_registrations;
DROP VIEW IF EXISTS patient_summary;

-- ===================================================================
-- Step 3: Fix SERIAL to BIGSERIAL column types
-- ===================================================================

-- Fix billing_referral.billing_id (SERIAL -> BIGSERIAL)
DO $$
BEGIN
    -- Check if billing_id is currently INTEGER (SERIAL)
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'billing_referral' 
        AND column_name = 'billing_id' 
        AND data_type = 'integer'
    ) THEN
        RAISE NOTICE 'Converting billing_referral.billing_id from SERIAL to BIGSERIAL';
        
        -- Convert the column type
        ALTER TABLE billing_referral ALTER COLUMN billing_id TYPE BIGINT;
        
        -- Update the sequence to use BIGINT
        ALTER SEQUENCE billing_referral_billing_id_seq AS BIGINT;
        
        RAISE NOTICE 'Successfully converted billing_referral.billing_id to BIGSERIAL';
    ELSE
        RAISE NOTICE 'billing_referral.billing_id is already BIGINT, skipping';
    END IF;
END $$;

-- Fix patient_contacts.contact_id (SERIAL -> BIGSERIAL)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'patient_contacts' 
        AND column_name = 'contact_id' 
        AND data_type = 'integer'
    ) THEN
        RAISE NOTICE 'Converting patient_contacts.contact_id from SERIAL to BIGSERIAL';
        ALTER TABLE patient_contacts ALTER COLUMN contact_id TYPE BIGINT;
        ALTER SEQUENCE patient_contacts_contact_id_seq AS BIGINT;
        RAISE NOTICE 'Successfully converted patient_contacts.contact_id to BIGSERIAL';
    ELSE
        RAISE NOTICE 'patient_contacts.contact_id is already BIGINT, skipping';
    END IF;
END $$;

-- Fix emergency_contacts.emergency_contact_id (SERIAL -> BIGSERIAL)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'emergency_contacts' 
        AND column_name = 'emergency_contact_id' 
        AND data_type = 'integer'
    ) THEN
        RAISE NOTICE 'Converting emergency_contacts.emergency_contact_id from SERIAL to BIGSERIAL';
        ALTER TABLE emergency_contacts ALTER COLUMN emergency_contact_id TYPE BIGINT;
        ALTER SEQUENCE emergency_contacts_emergency_contact_id_seq AS BIGINT;
        RAISE NOTICE 'Successfully converted emergency_contacts.emergency_contact_id to BIGSERIAL';
    ELSE
        RAISE NOTICE 'emergency_contacts.emergency_contact_id is already BIGINT, skipping';
    END IF;
END $$;

-- Fix patient_addresses.address_id (SERIAL -> BIGSERIAL)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'patient_addresses' 
        AND column_name = 'address_id' 
        AND data_type = 'integer'
    ) THEN
        RAISE NOTICE 'Converting patient_addresses.address_id from SERIAL to BIGSERIAL';
        ALTER TABLE patient_addresses ALTER COLUMN address_id TYPE BIGINT;
        ALTER SEQUENCE patient_addresses_address_id_seq AS BIGINT;
        RAISE NOTICE 'Successfully converted patient_addresses.address_id to BIGSERIAL';
    ELSE
        RAISE NOTICE 'patient_addresses.address_id is already BIGINT, skipping';
    END IF;
END $$;

-- Fix patient_abha.abha_id (SERIAL -> BIGSERIAL)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'patient_abha' 
        AND column_name = 'abha_id' 
        AND data_type = 'integer'
    ) THEN
        RAISE NOTICE 'Converting patient_abha.abha_id from SERIAL to BIGSERIAL';
        ALTER TABLE patient_abha ALTER COLUMN abha_id TYPE BIGINT;
        ALTER SEQUENCE patient_abha_abha_id_seq AS BIGINT;
        RAISE NOTICE 'Successfully converted patient_abha.abha_id to BIGSERIAL';
    ELSE
        RAISE NOTICE 'patient_abha.abha_id is already BIGINT, skipping';
    END IF;
END $$;

-- Fix patient_insurance.insurance_id (SERIAL -> BIGSERIAL)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'patient_insurance' 
        AND column_name = 'insurance_id' 
        AND data_type = 'integer'
    ) THEN
        RAISE NOTICE 'Converting patient_insurance.insurance_id from SERIAL to BIGSERIAL';
        ALTER TABLE patient_insurance ALTER COLUMN insurance_id TYPE BIGINT;
        ALTER SEQUENCE patient_insurance_insurance_id_seq AS BIGINT;
        RAISE NOTICE 'Successfully converted patient_insurance.insurance_id to BIGSERIAL';
    ELSE
        RAISE NOTICE 'patient_insurance.insurance_id is already BIGINT, skipping';
    END IF;
END $$;

-- Fix information_sharing.share_id (SERIAL -> BIGSERIAL)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'information_sharing'
        AND column_name = 'share_id'
        AND data_type = 'integer'
    ) THEN
        RAISE NOTICE 'Converting information_sharing.share_id from SERIAL to BIGSERIAL';
        ALTER TABLE information_sharing ALTER COLUMN share_id TYPE BIGINT;
        ALTER SEQUENCE information_sharing_share_id_seq AS BIGINT;
        RAISE NOTICE 'Successfully converted information_sharing.share_id to BIGSERIAL';
    ELSE
        RAISE NOTICE 'information_sharing.share_id is already BIGINT, skipping';
    END IF;
END $$;

-- Fix referrals.referral_id (SERIAL -> BIGSERIAL)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'referrals'
        AND column_name = 'referral_id'
        AND data_type = 'integer'
    ) THEN
        RAISE NOTICE 'Converting referrals.referral_id from SERIAL to BIGSERIAL';
        ALTER TABLE referrals ALTER COLUMN referral_id TYPE BIGINT;
        ALTER SEQUENCE referrals_referral_id_seq AS BIGINT;
        RAISE NOTICE 'Successfully converted referrals.referral_id to BIGSERIAL';
    ELSE
        RAISE NOTICE 'referrals.referral_id is already BIGINT, skipping';
    END IF;
END $$;

-- Fix patient_relationships.relationship_id (SERIAL -> BIGSERIAL)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'patient_relationships'
        AND column_name = 'relationship_id'
        AND data_type = 'integer'
    ) THEN
        RAISE NOTICE 'Converting patient_relationships.relationship_id from SERIAL to BIGSERIAL';
        ALTER TABLE patient_relationships ALTER COLUMN relationship_id TYPE BIGINT;
        ALTER SEQUENCE patient_relationships_relationship_id_seq AS BIGINT;
        RAISE NOTICE 'Successfully converted patient_relationships.relationship_id to BIGSERIAL';
    ELSE
        RAISE NOTICE 'patient_relationships.relationship_id is already BIGINT, skipping';
    END IF;
END $$;

-- ===================================================================
-- Step 4: Recreate the views (if they existed)
-- ===================================================================

-- Recreate patient_summary view if it existed
DO $$
DECLARE
    view_def TEXT;
BEGIN
    SELECT view_definition INTO view_def
    FROM temp_view_definitions_v101
    WHERE view_name = 'patient_summary';

    IF view_def IS NOT NULL THEN
        EXECUTE 'CREATE OR REPLACE VIEW patient_summary AS ' || view_def;
        RAISE NOTICE 'Recreated patient_summary view';
    ELSE
        -- Create the standard view if it didn't exist
        CREATE OR REPLACE VIEW patient_summary AS
        SELECT
            p.patient_id,
            p.facility_id,
            p.first_name,
            p.middle_name,
            p.last_name,
            CONCAT_WS(' ', p.first_name, p.middle_name, p.last_name) as full_name,
            p.date_of_birth,
            p.age,
            p.gender,
            p.blood_group,
            p.registration_date,
            pc.mobile_number,
            pc.email,
            pa.abha_number,
            p.is_active,
            p.soft_deleted
        FROM patients p
        LEFT JOIN patient_contacts pc ON p.patient_id = pc.patient_id
        LEFT JOIN patient_abha pa ON p.patient_id = pa.patient_id
        WHERE p.soft_deleted = false;

        RAISE NOTICE 'Created standard patient_summary view';
    END IF;
END $$;

-- Recreate recent_patient_registrations view if it existed
DO $$
DECLARE
    view_def TEXT;
BEGIN
    SELECT view_definition INTO view_def
    FROM temp_view_definitions_v101
    WHERE view_name = 'recent_patient_registrations';

    IF view_def IS NOT NULL THEN
        EXECUTE 'CREATE OR REPLACE VIEW recent_patient_registrations AS ' || view_def;
        RAISE NOTICE 'Recreated recent_patient_registrations view';
    ELSE
        -- Create the standard view if it didn't exist
        CREATE OR REPLACE VIEW recent_patient_registrations AS
        SELECT
            p.patient_id,
            p.facility_id,
            CONCAT_WS(' ', p.first_name, p.middle_name, p.last_name) as full_name,
            p.age,
            p.gender,
            p.registration_date,
            pc.mobile_number,
            pc.email
        FROM patients p
        LEFT JOIN patient_contacts pc ON p.patient_id = pc.patient_id
        WHERE p.soft_deleted = false
          AND p.registration_date >= CURRENT_DATE - INTERVAL '30 days'
        ORDER BY p.registration_date DESC;

        RAISE NOTICE 'Created standard recent_patient_registrations view';
    END IF;
END $$;

-- ===================================================================
-- Step 5: Add comments for documentation
-- ===================================================================

COMMENT ON TABLE billing_referral IS 'Patient billing and referral information - ID column converted to BIGSERIAL in V101';
COMMENT ON TABLE patient_contacts IS 'Patient contact information - ID column converted to BIGSERIAL in V101';
COMMENT ON TABLE emergency_contacts IS 'Emergency contact information - ID column converted to BIGSERIAL in V101';
COMMENT ON TABLE patient_addresses IS 'Patient address information - ID column converted to BIGSERIAL in V101';
COMMENT ON TABLE patient_abha IS 'Patient ABHA information - ID column converted to BIGSERIAL in V101';
COMMENT ON TABLE patient_insurance IS 'Patient insurance information - ID column converted to BIGSERIAL in V101';
COMMENT ON TABLE information_sharing IS 'Patient information sharing preferences - ID column converted to BIGSERIAL in V101';
COMMENT ON TABLE referrals IS 'Patient referral information - ID column converted to BIGSERIAL in V101';
COMMENT ON TABLE patient_relationships IS 'Patient relationship information - ID column converted to BIGSERIAL in V101';

-- ===================================================================
-- Step 6: Update table statistics for better query planning
-- ===================================================================

ANALYZE billing_referral;
ANALYZE patient_contacts;
ANALYZE emergency_contacts;
ANALYZE patient_addresses;
ANALYZE patient_abha;
ANALYZE patient_insurance;
ANALYZE information_sharing;
ANALYZE referrals;
ANALYZE patient_relationships;

-- ===================================================================
-- Step 7: Verify the migration
-- ===================================================================

-- Log successful completion
DO $$
BEGIN
    RAISE NOTICE 'Migration V101 completed successfully. All SERIAL columns converted to BIGSERIAL to match Hibernate entity expectations.';
    RAISE NOTICE 'The following columns were converted:';
    RAISE NOTICE '- billing_referral.billing_id: SERIAL -> BIGSERIAL';
    RAISE NOTICE '- patient_contacts.contact_id: SERIAL -> BIGSERIAL';
    RAISE NOTICE '- emergency_contacts.emergency_contact_id: SERIAL -> BIGSERIAL';
    RAISE NOTICE '- patient_addresses.address_id: SERIAL -> BIGSERIAL';
    RAISE NOTICE '- patient_abha.abha_id: SERIAL -> BIGSERIAL';
    RAISE NOTICE '- patient_insurance.insurance_id: SERIAL -> BIGSERIAL';
    RAISE NOTICE '- information_sharing.share_id: SERIAL -> BIGSERIAL';
    RAISE NOTICE '- referrals.referral_id: SERIAL -> BIGSERIAL';
    RAISE NOTICE '- patient_relationships.relationship_id: SERIAL -> BIGSERIAL';
    RAISE NOTICE 'Views recreated successfully. Schema validation should now pass.';
END $$;
