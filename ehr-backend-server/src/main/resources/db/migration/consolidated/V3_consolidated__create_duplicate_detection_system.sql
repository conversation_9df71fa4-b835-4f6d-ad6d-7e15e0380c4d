-- ===================================================================
-- EHR Database Migration V3 - Duplicate Detection System
-- Consolidated migration that creates duplicate detection tables with correct column types
-- ===================================================================

-- ===================================================================
-- Create Duplicate Detection Configuration Table
-- ===================================================================

CREATE TABLE duplicate_detection_config (
    config_id BIGSERIAL PRIMARY KEY,  -- Using BIGSERIAL from start
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value VARCHAR(500) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ===================================================================
-- Create Duplicate Detection Audit Log Table
-- ===================================================================

CREATE TABLE duplicate_detection_logs (
    log_id BIGSERIAL PRIMARY KEY,  -- Using BIGSERIAL from start
    patient_id VARCHAR,
    detection_type VARCHAR(20) NOT NULL, -- 'REGISTRATION', 'BATCH', 'MANUAL'
    match_score INTEGER NOT NULL,
    confidence_level VARCHAR(10) NOT NULL, -- 'HIGH', 'MEDIUM', 'LOW'
    potential_duplicates TEXT, -- Using TEXT instead of JSONB for Hibernate compatibility
    matching_criteria TEXT, -- Using TEXT instead of JSONB for Hibernate compatibility
    action_taken VARCHAR(20), -- 'BLOCKED', 'FLAGGED', 'APPROVED', 'MERGED'
    reviewed_by VARCHAR,
    review_notes TEXT,
    detection_time TIMESTAMP WITH TIME ZONE DEFAULT now(),
    review_time TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT fk_duplicate_logs_patient
        FOREIGN KEY (patient_id)
        REFERENCES patients(patient_id)
        ON DELETE SET NULL
);

-- ===================================================================
-- Create Duplicate Patient Relationships Table
-- ===================================================================

CREATE TABLE duplicate_patient_relationships (
    relationship_id BIGSERIAL PRIMARY KEY,  -- Using BIGSERIAL from start
    primary_patient_id VARCHAR NOT NULL,
    duplicate_patient_id VARCHAR NOT NULL,
    relationship_type VARCHAR(20) NOT NULL, -- 'CONFIRMED_DUPLICATE', 'FALSE_POSITIVE'
    confidence_score INTEGER NOT NULL,
    identified_by VARCHAR, -- User who confirmed the relationship
    identified_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    
    CONSTRAINT fk_duplicate_rel_primary
        FOREIGN KEY (primary_patient_id)
        REFERENCES patients(patient_id)
        ON DELETE CASCADE,
    CONSTRAINT fk_duplicate_rel_duplicate
        FOREIGN KEY (duplicate_patient_id)
        REFERENCES patients(patient_id)
        ON DELETE CASCADE,
    CONSTRAINT unique_duplicate_relationship
        UNIQUE (primary_patient_id, duplicate_patient_id)
);

-- ===================================================================
-- Create Indexes for Performance
-- ===================================================================

CREATE INDEX idx_duplicate_logs_patient_id ON duplicate_detection_logs(patient_id);
CREATE INDEX idx_duplicate_logs_detection_time ON duplicate_detection_logs(detection_time);
CREATE INDEX idx_duplicate_logs_confidence ON duplicate_detection_logs(confidence_level);
CREATE INDEX idx_duplicate_rel_primary ON duplicate_patient_relationships(primary_patient_id);
CREATE INDEX idx_duplicate_rel_duplicate ON duplicate_patient_relationships(duplicate_patient_id);
CREATE INDEX idx_duplicate_config_key ON duplicate_detection_config(config_key);

-- ===================================================================
-- Insert Default Configuration Values
-- ===================================================================

INSERT INTO duplicate_detection_config (config_key, config_value, description) VALUES
('duplicate.detection.enabled', 'true', 'Enable/disable duplicate detection during patient registration'),
('duplicate.detection.threshold.high', '85', 'High confidence threshold score (0-100) for blocking registration'),
('duplicate.detection.threshold.medium', '70', 'Medium confidence threshold score (0-100) for manual review'),
('duplicate.detection.weight.name.exact', '40', 'Weight for exact name match'),
('duplicate.detection.weight.name.fuzzy', '25', 'Weight for fuzzy name match'),
('duplicate.detection.weight.dob.exact', '30', 'Weight for exact date of birth match'),
('duplicate.detection.weight.phone', '20', 'Weight for phone number match'),
('duplicate.detection.weight.email', '15', 'Weight for email match'),
('duplicate.detection.weight.address', '10', 'Weight for address match'),
('duplicate.detection.weight.identifier', '35', 'Weight for national identifier match (ABHA, Aadhaar, etc.)'),
('duplicate.detection.fuzzy.threshold', '80', 'Minimum similarity percentage for fuzzy string matching'),
('duplicate.detection.timeout.seconds', '2', 'Maximum time allowed for duplicate detection check'),
('duplicate.detection.batch.enabled', 'true', 'Enable batch duplicate detection for data cleanup'),
('duplicate.detection.audit.enabled', 'true', 'Enable audit logging for duplicate detection events');

-- ===================================================================
-- Add Comments for Documentation
-- ===================================================================

COMMENT ON TABLE duplicate_detection_config IS 'Configuration settings for duplicate detection system - ID column uses BIGSERIAL';
COMMENT ON TABLE duplicate_detection_logs IS 'Audit logs for duplicate detection events - ID column uses BIGSERIAL, JSON columns use TEXT';
COMMENT ON TABLE duplicate_patient_relationships IS 'Confirmed duplicate patient relationships - ID column uses BIGSERIAL';

COMMENT ON COLUMN duplicate_detection_logs.potential_duplicates IS 'JSON array of potential duplicate patient IDs with scores - stored as TEXT for Hibernate compatibility';
COMMENT ON COLUMN duplicate_detection_logs.matching_criteria IS 'JSON details of what criteria matched - stored as TEXT for Hibernate compatibility';

-- ===================================================================
-- Update Table Statistics
-- ===================================================================

ANALYZE duplicate_detection_config;
ANALYZE duplicate_detection_logs;
ANALYZE duplicate_patient_relationships;

-- ===================================================================
-- Log Completion
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE 'Migration V3_consolidated completed successfully. Created duplicate detection system:';
    RAISE NOTICE '- duplicate_detection_config: Configuration with BIGSERIAL ID and default values';
    RAISE NOTICE '- duplicate_detection_logs: Audit logs with BIGSERIAL ID and TEXT JSON columns';
    RAISE NOTICE '- duplicate_patient_relationships: Relationships with BIGSERIAL ID';
    RAISE NOTICE 'All tables created with proper indexes and Hibernate-compatible column types.';
END $$;
