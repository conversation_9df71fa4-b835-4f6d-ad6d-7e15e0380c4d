-- ===================================================================
-- EHR Database Migration V2 - Patient Management System
-- Consolidated migration that creates all patient-related tables with correct column types
-- ===================================================================

-- ===================================================================
-- Create Main Patients Table
-- ===================================================================

CREATE TABLE patients (
    patient_id         VARCHAR PRIMARY KEY,
    facility_id        VARCHAR,
    identifier_type    VARCHAR,
    identifier_number  VARCHAR,
    title              VARCHAR,
    first_name         VARCHAR,
    middle_name        VARCHAR,
    last_name          VARCHAR,
    date_of_birth      DATE,
    age                INTEGER,
    gender             VARCHAR,
    blood_group        VARCHAR,
    marital_status     VARCHAR,
    citizenship        VARCHAR,
    religion           VARCHAR,
    caste              VARCHAR,
    occupation         VARCHAR,
    education          VARCHAR,
    annual_income      VARCHAR,
    registration_date  TIMESTAMP WITH TIME ZONE DEFAULT now(),
    is_active          BOOLEAN DEFAULT true,
    is_deceased        BOOLEAN DEFAULT false,
    soft_deleted       BOOLEAN DEFAULT false,

    CONSTRAINT fk_patients_facility
      FOREIGN KEY (facility_id)
      REFERENCES facility(hospital_id)
      ON DELETE RESTRICT
      ON UPDATE CASCADE
);

-- ===================================================================
-- Create Patient Contact Information Table
-- ===================================================================

CREATE TABLE patient_contacts (
    contact_id             BIGSERIAL PRIMARY KEY,  -- Using BIGSERIAL from start
    patient_id             VARCHAR,
    mobile_number          VARCHAR,
    phone_number           VARCHAR,
    email                  VARCHAR,
    preferred_contact_mode VARCHAR,
    phone_contact_preference VARCHAR,
    consent_to_share       BOOLEAN DEFAULT false,

    CONSTRAINT fk_patient_contacts_patient
      FOREIGN KEY (patient_id)
      REFERENCES patients(patient_id)
      ON DELETE CASCADE
);

-- ===================================================================
-- Create Emergency Contacts Table
-- ===================================================================

CREATE TABLE emergency_contacts (
    emergency_contact_id   BIGSERIAL PRIMARY KEY,  -- Using BIGSERIAL from start
    patient_id             VARCHAR,
    contact_name           VARCHAR,
    relationship           VARCHAR,
    phone_number           VARCHAR,

    CONSTRAINT fk_emergency_contacts_patient
      FOREIGN KEY (patient_id)
      REFERENCES patients(patient_id)
      ON DELETE CASCADE
);

-- ===================================================================
-- Create Patient Addresses Table
-- ===================================================================

CREATE TABLE patient_addresses (
    address_id         BIGSERIAL PRIMARY KEY,  -- Using BIGSERIAL from start
    patient_id         VARCHAR,
    address_type       VARCHAR,
    house_no_or_flat_no VARCHAR,
    locality_or_sector VARCHAR,
    city_or_village    VARCHAR,
    pincode           VARCHAR,
    district_id        VARCHAR,
    state_id           VARCHAR,
    country            VARCHAR DEFAULT 'India',

    CONSTRAINT fk_patient_addresses_patient
      FOREIGN KEY (patient_id)
      REFERENCES patients(patient_id)
      ON DELETE CASCADE
);

-- ===================================================================
-- Create Patient ABHA Information Table
-- ===================================================================

CREATE TABLE patient_abha (
    abha_id        BIGSERIAL PRIMARY KEY,  -- Using BIGSERIAL from start
    patient_id     VARCHAR,
    abha_number    VARCHAR,
    abha_address   VARCHAR,

    CONSTRAINT fk_patient_abha_patient
      FOREIGN KEY (patient_id)
      REFERENCES patients(patient_id)
      ON DELETE CASCADE
);

-- ===================================================================
-- Create Billing and Referral Table
-- ===================================================================

CREATE TABLE billing_referral (
    billing_id     BIGSERIAL PRIMARY KEY,  -- Using BIGSERIAL from start
    patient_id     VARCHAR,
    billing_type   VARCHAR,
    referred_by    VARCHAR,

    CONSTRAINT fk_billing_referral_patient
      FOREIGN KEY (patient_id)
      REFERENCES patients(patient_id)
      ON DELETE CASCADE
);

-- ===================================================================
-- Create Information Sharing Preferences Table
-- ===================================================================

CREATE TABLE information_sharing (
    share_id               BIGSERIAL PRIMARY KEY,  -- Using BIGSERIAL from start
    patient_id             VARCHAR,
    share_with_spouse      BOOLEAN DEFAULT false,
    share_with_children    BOOLEAN DEFAULT false,
    share_with_caregiver   BOOLEAN DEFAULT false,
    share_with_other       BOOLEAN DEFAULT false,

    CONSTRAINT fk_information_sharing_patient
      FOREIGN KEY (patient_id)
      REFERENCES patients(patient_id)
      ON DELETE CASCADE
);

-- ===================================================================
-- Create Patient Insurance Table
-- ===================================================================

CREATE TABLE patient_insurance (
    insurance_id     BIGSERIAL PRIMARY KEY,  -- Using BIGSERIAL from start
    patient_id       VARCHAR UNIQUE, -- Only one insurance per patient
    insurance_provider VARCHAR,
    policy_number    VARCHAR,
    policy_start_date DATE,
    policy_end_date   DATE,
    coverage_amount  NUMERIC,

    CONSTRAINT fk_patient_insurance_patient
      FOREIGN KEY (patient_id)
      REFERENCES patients(patient_id)
      ON DELETE CASCADE
);

-- ===================================================================
-- Create Referrals Table
-- ===================================================================

CREATE TABLE referrals (
    referral_id     BIGSERIAL PRIMARY KEY,  -- Using BIGSERIAL from start
    patient_id      VARCHAR,
    from_facility_id VARCHAR,
    to_facility_id   VARCHAR,
    referral_date    DATE DEFAULT CURRENT_DATE,
    reason           VARCHAR,

    CONSTRAINT fk_referrals_patient
      FOREIGN KEY (patient_id)
      REFERENCES patients(patient_id)
      ON DELETE CASCADE
);

-- ===================================================================
-- Create Patient Relationships Table
-- ===================================================================

CREATE TABLE patient_relationships (
    relationship_id   BIGSERIAL PRIMARY KEY,  -- Using BIGSERIAL from start
    patient_id        VARCHAR,
    relative_id       UUID,
    relationship_type VARCHAR,

    CONSTRAINT fk_patient_relationships_patient
      FOREIGN KEY (patient_id)
      REFERENCES patients(patient_id)
      ON DELETE CASCADE
);

-- ===================================================================
-- Add Data Validation Constraints
-- ===================================================================

-- Ensure ABHA number format is correct
ALTER TABLE patient_abha
ADD CONSTRAINT chk_abha_number_format
CHECK (abha_number IS NULL OR abha_number ~ '^\d{2}-\d{4}-\d{4}-\d{4}$');

-- Ensure mobile number format is correct
ALTER TABLE patient_contacts
ADD CONSTRAINT chk_mobile_format
CHECK (mobile_number IS NULL OR mobile_number ~ '^[+]?[0-9]{10,15}$');

-- Ensure email format is correct
ALTER TABLE patient_contacts
ADD CONSTRAINT chk_email_format
CHECK (email IS NULL OR email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Ensure age is reasonable
ALTER TABLE patients
ADD CONSTRAINT chk_age_range
CHECK (age IS NULL OR (age >= 0 AND age <= 150));

-- Ensure date of birth is not in the future
ALTER TABLE patients
ADD CONSTRAINT chk_date_of_birth
CHECK (date_of_birth IS NULL OR date_of_birth <= CURRENT_DATE);

-- Ensure policy dates are logical
ALTER TABLE patient_insurance
ADD CONSTRAINT chk_policy_dates
CHECK (policy_start_date IS NULL OR policy_end_date IS NULL OR policy_start_date <= policy_end_date);

-- ===================================================================
-- Add Comments for Documentation
-- ===================================================================

COMMENT ON TABLE patients IS 'Main patient information table with optimized column types';
COMMENT ON TABLE patient_contacts IS 'Patient contact information - ID column uses BIGSERIAL';
COMMENT ON TABLE emergency_contacts IS 'Emergency contact information - ID column uses BIGSERIAL';
COMMENT ON TABLE patient_addresses IS 'Patient address information - ID column uses BIGSERIAL';
COMMENT ON TABLE patient_abha IS 'Patient ABHA information - ID column uses BIGSERIAL';
COMMENT ON TABLE billing_referral IS 'Patient billing and referral information - ID column uses BIGSERIAL';
COMMENT ON TABLE information_sharing IS 'Patient information sharing preferences - ID column uses BIGSERIAL';
COMMENT ON TABLE patient_insurance IS 'Patient insurance information - ID column uses BIGSERIAL';
COMMENT ON TABLE referrals IS 'Patient referral information - ID column uses BIGSERIAL';
COMMENT ON TABLE patient_relationships IS 'Patient relationship information - ID column uses BIGSERIAL';

-- ===================================================================
-- Update Table Statistics
-- ===================================================================

ANALYZE patients;
ANALYZE patient_contacts;
ANALYZE emergency_contacts;
ANALYZE patient_addresses;
ANALYZE patient_abha;
ANALYZE billing_referral;
ANALYZE information_sharing;
ANALYZE patient_insurance;
ANALYZE referrals;
ANALYZE patient_relationships;

-- ===================================================================
-- Log Completion
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE 'Migration V2_consolidated completed successfully. Created patient management system:';
    RAISE NOTICE '- patients: Main patient information table';
    RAISE NOTICE '- patient_contacts: Contact information with BIGSERIAL ID';
    RAISE NOTICE '- emergency_contacts: Emergency contacts with BIGSERIAL ID';
    RAISE NOTICE '- patient_addresses: Address information with BIGSERIAL ID';
    RAISE NOTICE '- patient_abha: ABHA information with BIGSERIAL ID';
    RAISE NOTICE '- billing_referral: Billing/referral with BIGSERIAL ID';
    RAISE NOTICE '- information_sharing: Sharing preferences with BIGSERIAL ID';
    RAISE NOTICE '- patient_insurance: Insurance information with BIGSERIAL ID';
    RAISE NOTICE '- referrals: Referral information with BIGSERIAL ID';
    RAISE NOTICE '- patient_relationships: Relationship information with BIGSERIAL ID';
    RAISE NOTICE 'All tables created with proper constraints and BIGSERIAL IDs from start.';
END $$;
