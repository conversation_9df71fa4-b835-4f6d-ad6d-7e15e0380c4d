-- ===================================================================
-- Schema Verification Script
-- This script can be used to verify that the consolidated migrations
-- produce the same final schema as the original migrations
-- ===================================================================

-- ===================================================================
-- Table Existence Verification
-- ===================================================================

-- Check that all expected tables exist
DO $$
DECLARE
    expected_tables TEXT[] := ARRAY[
        'facility',
        'lookup_values', 
        'countries',
        'states',
        'districts',
        'patients',
        'patient_contacts',
        'emergency_contacts',
        'patient_addresses',
        'patient_abha',
        'billing_referral',
        'information_sharing',
        'patient_insurance',
        'referrals',
        'patient_relationships',
        'duplicate_detection_config',
        'duplicate_detection_logs',
        'duplicate_patient_relationships'
    ];
    table_name TEXT;
    missing_tables TEXT[] := '{}';
BEGIN
    FOREACH table_name IN ARRAY expected_tables
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_name = table_name AND table_schema = 'public'
        ) THEN
            missing_tables := array_append(missing_tables, table_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_tables, 1) > 0 THEN
        RAISE EXCEPTION 'Missing tables: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE 'All expected tables exist: %', array_to_string(expected_tables, ', ');
    END IF;
END $$;

-- ===================================================================
-- Column Type Verification
-- ===================================================================

-- Verify BIGSERIAL columns are correct
DO $$
DECLARE
    bigserial_columns RECORD;
    incorrect_types TEXT[] := '{}';
BEGIN
    FOR bigserial_columns IN
        SELECT table_name, column_name, data_type
        FROM information_schema.columns
        WHERE table_name IN (
            'patient_contacts', 'emergency_contacts', 'patient_addresses',
            'patient_abha', 'billing_referral', 'information_sharing',
            'patient_insurance', 'referrals', 'patient_relationships',
            'duplicate_detection_config', 'duplicate_detection_logs',
            'duplicate_patient_relationships'
        )
        AND column_name LIKE '%_id'
        AND column_name != 'patient_id'
        AND data_type != 'bigint'
    LOOP
        incorrect_types := array_append(incorrect_types, 
            bigserial_columns.table_name || '.' || bigserial_columns.column_name || 
            ' (' || bigserial_columns.data_type || ')');
    END LOOP;
    
    IF array_length(incorrect_types, 1) > 0 THEN
        RAISE EXCEPTION 'Incorrect ID column types: %', array_to_string(incorrect_types, ', ');
    ELSE
        RAISE NOTICE 'All ID columns are correctly typed as BIGINT';
    END IF;
END $$;

-- Verify TEXT columns for JSON data
DO $$
DECLARE
    json_columns RECORD;
    incorrect_json_types TEXT[] := '{}';
BEGIN
    FOR json_columns IN
        SELECT table_name, column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'duplicate_detection_logs'
        AND column_name IN ('potential_duplicates', 'matching_criteria')
        AND data_type != 'text'
    LOOP
        incorrect_json_types := array_append(incorrect_json_types,
            json_columns.table_name || '.' || json_columns.column_name ||
            ' (' || json_columns.data_type || ')');
    END LOOP;
    
    IF array_length(incorrect_json_types, 1) > 0 THEN
        RAISE EXCEPTION 'Incorrect JSON column types: %', array_to_string(incorrect_json_types, ', ');
    ELSE
        RAISE NOTICE 'All JSON columns are correctly typed as TEXT';
    END IF;
END $$;

-- ===================================================================
-- Foreign Key Verification
-- ===================================================================

-- Check that all expected foreign keys exist
DO $$
DECLARE
    expected_fks RECORD;
    missing_fks TEXT[] := '{}';
    fk_count INTEGER;
BEGIN
    -- Define expected foreign keys
    FOR expected_fks IN VALUES
        ('patients', 'fk_patients_facility'),
        ('patient_contacts', 'fk_patient_contacts_patient'),
        ('emergency_contacts', 'fk_emergency_contacts_patient'),
        ('patient_addresses', 'fk_patient_addresses_patient'),
        ('patient_abha', 'fk_patient_abha_patient'),
        ('billing_referral', 'fk_billing_referral_patient'),
        ('information_sharing', 'fk_information_sharing_patient'),
        ('patient_insurance', 'fk_patient_insurance_patient'),
        ('referrals', 'fk_referrals_patient'),
        ('patient_relationships', 'fk_patient_relationships_patient'),
        ('states', 'fk_states_country'),
        ('districts', 'fk_districts_state'),
        ('duplicate_detection_logs', 'fk_duplicate_logs_patient'),
        ('duplicate_patient_relationships', 'fk_duplicate_rel_primary'),
        ('duplicate_patient_relationships', 'fk_duplicate_rel_duplicate')
    LOOP
        SELECT COUNT(*) INTO fk_count
        FROM information_schema.table_constraints
        WHERE table_name = expected_fks.column1
        AND constraint_name = expected_fks.column2
        AND constraint_type = 'FOREIGN KEY';
        
        IF fk_count = 0 THEN
            missing_fks := array_append(missing_fks, 
                expected_fks.column1 || '.' || expected_fks.column2);
        END IF;
    END LOOP;
    
    IF array_length(missing_fks, 1) > 0 THEN
        RAISE EXCEPTION 'Missing foreign keys: %', array_to_string(missing_fks, ', ');
    ELSE
        RAISE NOTICE 'All expected foreign keys exist';
    END IF;
END $$;

-- ===================================================================
-- Index Verification
-- ===================================================================

-- Check that key indexes exist
DO $$
DECLARE
    expected_indexes TEXT[] := ARRAY[
        'idx_patients_facility_id',
        'idx_patients_search_composite',
        'idx_patient_contacts_patient_id',
        'idx_patient_abha_number',
        'idx_duplicate_logs_patient_id',
        'idx_duplicate_config_key',
        'idx_countries_name',
        'idx_states_country_id',
        'idx_districts_state_id'
    ];
    index_name TEXT;
    missing_indexes TEXT[] := '{}';
BEGIN
    FOREACH index_name IN ARRAY expected_indexes
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM pg_indexes 
            WHERE indexname = index_name AND schemaname = 'public'
        ) THEN
            missing_indexes := array_append(missing_indexes, index_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_indexes, 1) > 0 THEN
        RAISE EXCEPTION 'Missing indexes: %', array_to_string(missing_indexes, ', ');
    ELSE
        RAISE NOTICE 'All key indexes exist';
    END IF;
END $$;

-- ===================================================================
-- View Verification
-- ===================================================================

-- Check that expected views exist
DO $$
DECLARE
    expected_views TEXT[] := ARRAY[
        'patient_summary',
        'patient_stats_by_facility',
        'recent_patient_registrations'
    ];
    view_name TEXT;
    missing_views TEXT[] := '{}';
BEGIN
    FOREACH view_name IN ARRAY expected_views
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.views 
            WHERE table_name = view_name AND table_schema = 'public'
        ) THEN
            missing_views := array_append(missing_views, view_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_views, 1) > 0 THEN
        RAISE EXCEPTION 'Missing views: %', array_to_string(missing_views, ', ');
    ELSE
        RAISE NOTICE 'All expected views exist';
    END IF;
END $$;

-- ===================================================================
-- Function Verification
-- ===================================================================

-- Check that expected functions exist
DO $$
DECLARE
    expected_functions TEXT[] := ARRAY[
        'calculate_age',
        'format_patient_name',
        'is_valid_abha_number',
        'update_patient_age'
    ];
    function_name TEXT;
    missing_functions TEXT[] := '{}';
BEGIN
    FOREACH function_name IN ARRAY expected_functions
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.routines 
            WHERE routine_name = function_name AND routine_schema = 'public'
        ) THEN
            missing_functions := array_append(missing_functions, function_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_functions, 1) > 0 THEN
        RAISE EXCEPTION 'Missing functions: %', array_to_string(missing_functions, ', ');
    ELSE
        RAISE NOTICE 'All expected functions exist';
    END IF;
END $$;

-- ===================================================================
-- Data Verification
-- ===================================================================

-- Check that reference data exists
DO $$
DECLARE
    country_count INTEGER;
    state_count INTEGER;
    config_count INTEGER;
BEGIN
    -- Check countries
    SELECT COUNT(*) INTO country_count FROM countries WHERE name = 'India';
    IF country_count = 0 THEN
        RAISE EXCEPTION 'India not found in countries table';
    END IF;
    
    -- Check states
    SELECT COUNT(*) INTO state_count FROM states;
    IF state_count < 28 THEN
        RAISE EXCEPTION 'Insufficient states in states table: %', state_count;
    END IF;
    
    -- Check duplicate detection config
    SELECT COUNT(*) INTO config_count FROM duplicate_detection_config;
    IF config_count < 10 THEN
        RAISE EXCEPTION 'Insufficient config entries: %', config_count;
    END IF;
    
    RAISE NOTICE 'Reference data verification passed';
END $$;

-- ===================================================================
-- Final Verification Summary
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE '=== SCHEMA VERIFICATION COMPLETED SUCCESSFULLY ===';
    RAISE NOTICE 'All tables, columns, foreign keys, indexes, views, functions, and reference data verified.';
    RAISE NOTICE 'The consolidated migrations have produced the expected database schema.';
END $$;
