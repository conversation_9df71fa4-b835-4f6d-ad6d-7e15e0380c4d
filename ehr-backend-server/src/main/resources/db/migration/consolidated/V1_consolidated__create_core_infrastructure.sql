-- ===================================================================
-- EHR Database Migration V1 - Core Infrastructure
-- Consolidated migration that creates core infrastructure tables
-- ===================================================================

-- ===================================================================
-- Create Facility Table
-- ===================================================================

CREATE TABLE facility (
    hospital_id        VARCHAR NOT NULL PRIMARY KEY,
    facility_name      VARCHAR,
    facility_type      VARCHAR,
    block              VARCHAR,
    phc_chc_name       VARCHAR,
    location           VARCHAR,
    officer_in_charge  VARCHAR,
    designation        VARCHAR,
    contact_number     VARCHAR,
    official_email     VARCHAR,
    network_id         VARCHAR,
    bed_strength       INTEGER,
    patient_types      VARCHAR,
    notes              TEXT,
    equipments         TEXT,
    created_at         TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at         TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ===================================================================
-- Create Lookup Values Table
-- ===================================================================

CREATE TABLE lookup_values (
    id           UUID PRIMARY KEY,
    category     VARCHAR,
    code         VARCHAR,
    display_name VARCHAR,
    sort_order   INTEGER,
    active       BOOLEAN,
    created_at   TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at   TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ===================================================================
-- Create Geographic Lookup Tables
-- ===================================================================

-- Countries Table
CREATE TABLE countries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- States Table
CREATE TABLE states (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    country_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_states_country
        FOREIGN KEY (country_id)
        REFERENCES countries(id)
        ON DELETE CASCADE
);

-- Districts Table
CREATE TABLE districts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    state_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_districts_state
        FOREIGN KEY (state_id)
        REFERENCES states(id)
        ON DELETE CASCADE
);

-- ===================================================================
-- Create Basic Indexes
-- ===================================================================

-- Geographic table indexes
CREATE INDEX idx_countries_name ON countries(name);
CREATE INDEX idx_states_country_id ON states(country_id);
CREATE INDEX idx_states_name ON states(name);
CREATE INDEX idx_districts_state_id ON districts(state_id);
CREATE INDEX idx_districts_name ON districts(name);

-- ===================================================================
-- Insert Reference Data
-- ===================================================================

-- Insert India as the default country
INSERT INTO countries (id, name) VALUES 
    ('550e8400-e29b-41d4-a716-************', 'India');

-- Insert Indian states
INSERT INTO states (id, country_id, name) VALUES 
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Andhra Pradesh'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Arunachal Pradesh'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Assam'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Bihar'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Chhattisgarh'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Goa'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Gujarat'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Haryana'),
    ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Himachal Pradesh'),
    ('550e8400-e29b-41d4-a716-446655440010', '550e8400-e29b-41d4-a716-************', 'Jharkhand'),
    ('550e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-************', 'Karnataka'),
    ('550e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-************', 'Kerala'),
    ('550e8400-e29b-41d4-a716-446655440013', '550e8400-e29b-41d4-a716-************', 'Madhya Pradesh'),
    ('550e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-************', 'Maharashtra'),
    ('550e8400-e29b-41d4-a716-446655440015', '550e8400-e29b-41d4-a716-************', 'Manipur'),
    ('550e8400-e29b-41d4-a716-446655440016', '550e8400-e29b-41d4-a716-************', 'Meghalaya'),
    ('550e8400-e29b-41d4-a716-446655440017', '550e8400-e29b-41d4-a716-************', 'Mizoram'),
    ('550e8400-e29b-41d4-a716-446655440018', '550e8400-e29b-41d4-a716-************', 'Nagaland'),
    ('550e8400-e29b-41d4-a716-446655440019', '550e8400-e29b-41d4-a716-************', 'Odisha'),
    ('550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-************', 'Punjab'),
    ('550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-************', 'Rajasthan'),
    ('550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-************', 'Sikkim'),
    ('550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-************', 'Tamil Nadu'),
    ('550e8400-e29b-41d4-a716-446655440024', '550e8400-e29b-41d4-a716-************', 'Telangana'),
    ('550e8400-e29b-41d4-a716-446655440025', '550e8400-e29b-41d4-a716-************', 'Tripura'),
    ('550e8400-e29b-41d4-a716-446655440026', '550e8400-e29b-41d4-a716-************', 'Uttar Pradesh'),
    ('550e8400-e29b-41d4-a716-446655440027', '550e8400-e29b-41d4-a716-************', 'Uttarakhand'),
    ('550e8400-e29b-41d4-a716-446655440028', '550e8400-e29b-41d4-a716-************', 'West Bengal'),
    ('550e8400-e29b-41d4-a716-446655440029', '550e8400-e29b-41d4-a716-************', 'Delhi'),
    ('550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-************', 'Jammu and Kashmir'),
    ('550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-************', 'Ladakh');

-- Insert sample districts for Meghalaya
INSERT INTO districts (id, state_id, name) VALUES
    ('550e8400-e29b-41d4-a716-446655440100', '550e8400-e29b-41d4-a716-446655440016', 'East Garo Hills'),
    ('550e8400-e29b-41d4-a716-446655440101', '550e8400-e29b-41d4-a716-446655440016', 'East Jaintia Hills'),
    ('550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440016', 'East Khasi Hills'),
    ('550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440016', 'North Garo Hills'),
    ('550e8400-e29b-41d4-a716-446655440104', '550e8400-e29b-41d4-a716-446655440016', 'Ri Bhoi'),
    ('550e8400-e29b-41d4-a716-446655440105', '550e8400-e29b-41d4-a716-446655440016', 'South Garo Hills'),
    ('550e8400-e29b-41d4-a716-446655440106', '550e8400-e29b-41d4-a716-446655440016', 'South West Garo Hills'),
    ('550e8400-e29b-41d4-a716-446655440107', '550e8400-e29b-41d4-a716-446655440016', 'South West Khasi Hills'),
    ('550e8400-e29b-41d4-a716-446655440108', '550e8400-e29b-41d4-a716-446655440016', 'West Garo Hills'),
    ('550e8400-e29b-41d4-a716-446655440109', '550e8400-e29b-41d4-a716-446655440016', 'West Jaintia Hills'),
    ('550e8400-e29b-41d4-a716-446655440110', '550e8400-e29b-41d4-a716-446655440016', 'West Khasi Hills');

-- ===================================================================
-- Add Comments for Documentation
-- ===================================================================

COMMENT ON TABLE facility IS 'Healthcare facility information';
COMMENT ON TABLE lookup_values IS 'Generic lookup values for various categories';
COMMENT ON TABLE countries IS 'Geographic lookup table for countries';
COMMENT ON TABLE states IS 'Geographic lookup table for states/provinces within countries';
COMMENT ON TABLE districts IS 'Geographic lookup table for districts within states';

-- ===================================================================
-- Update Table Statistics
-- ===================================================================

ANALYZE facility;
ANALYZE lookup_values;
ANALYZE countries;
ANALYZE states;
ANALYZE districts;

-- ===================================================================
-- Log Completion
-- ===================================================================

DO $$
BEGIN
    RAISE NOTICE 'Migration V1_consolidated completed successfully. Created core infrastructure:';
    RAISE NOTICE '- facility: Healthcare facility information';
    RAISE NOTICE '- lookup_values: Generic lookup values';
    RAISE NOTICE '- countries: Geographic countries with India as default';
    RAISE NOTICE '- states: All Indian states';
    RAISE NOTICE '- districts: Sample Meghalaya districts';
END $$;
